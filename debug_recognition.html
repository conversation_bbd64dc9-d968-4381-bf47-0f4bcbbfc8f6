<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recognition选择题调试</title>
    <link rel="stylesheet" href="static/css/learning-page.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-info {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .debug-info h3 {
            margin-top: 0;
            color: #007bff;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <h1>Recognition选择题调试页面</h1>
    
    <div class="debug-info">
        <h3>测试控制</h3>
        <button class="test-button" onclick="testRecognitionDisplay()">测试选择题显示</button>
        <button class="test-button" onclick="testDOMElements()">检查DOM元素</button>
        <button class="test-button" onclick="testAPICall()">测试API调用</button>
        <button class="test-button" onclick="clearConsole()">清空控制台</button>
    </div>

    <div class="debug-info">
        <h3>Recognition模式界面</h3>
        <!-- Recognition模式（选择题模式） -->
        <div id="recognition-mode" style="display: block;">
            <div id="recognition-container">
                <!-- 问题显示区域 -->
                <div id="recognition-question">
                    <div id="question-text">请选择正确答案：</div>
                </div>
                
                <!-- 选择题选项区域 -->
                <div id="recognition-options">
                    <div class="option-button" data-option="A" onclick="selectRecognitionOption('A')">
                        <span class="option-label">A</span>
                        <span class="option-text" id="option-A-text">选项A</span>
                    </div>
                    <div class="option-button" data-option="B" onclick="selectRecognitionOption('B')">
                        <span class="option-label">B</span>
                        <span class="option-text" id="option-B-text">选项B</span>
                    </div>
                    <div class="option-button" data-option="C" onclick="selectRecognitionOption('C')">
                        <span class="option-label">C</span>
                        <span class="option-text" id="option-C-text">选项C</span>
                    </div>
                    <div class="option-button" data-option="D" onclick="selectRecognitionOption('D')">
                        <span class="option-label">D</span>
                        <span class="option-text" id="option-D-text">选项D</span>
                    </div>
                </div>
                
                <!-- 确认按钮 -->
                <div id="recognition-submit">
                    <button id="recognition-confirm-btn" onclick="confirmRecognitionAnswer()" disabled>确认答案</button>
                </div>
            </div>
        </div>
    </div>

    <div class="debug-info">
        <h3>调试信息</h3>
        <div id="debug-output"></div>
    </div>

    <script>
        // 模拟全局变量
        let currentRecognitionData = null;
        let selectedRecognitionOptions = [];

        function clearConsole() {
            console.clear();
            document.getElementById('debug-output').innerHTML = '';
        }

        function log(message) {
            console.log(message);
            const debugOutput = document.getElementById('debug-output');
            debugOutput.innerHTML += '<div>' + message + '</div>';
        }

        function testDOMElements() {
            log('=== DOM元素检查 ===');
            
            const elements = [
                'recognition-mode',
                'recognition-container', 
                'recognition-question',
                'question-text',
                'recognition-options',
                'option-A-text',
                'option-B-text', 
                'option-C-text',
                'option-D-text'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                log(`${id}: ${element ? '✅ 存在' : '❌ 不存在'}`);
            });

            const buttons = ['A', 'B', 'C', 'D'];
            buttons.forEach(key => {
                const button = document.querySelector(`[data-option="${key}"]`);
                log(`按钮${key}: ${button ? '✅ 存在' : '❌ 不存在'}`);
            });
        }

        function testRecognitionDisplay() {
            log('=== 测试选择题显示 ===');
            
            // 模拟选择题数据
            currentRecognitionData = {
                word_id: 1,
                question: "苹果",
                question_type: "cn_to_en",
                options: ["apple", "banana", "orange", "grape"],
                correct_answers: ["apple"],
                is_multiple_choice: false,
                total_meanings: 1
            };

            log('设置测试数据: ' + JSON.stringify(currentRecognitionData));
            
            // 调用显示函数
            displayRecognitionQuestion();
        }

        function testAPICall() {
            log('=== 测试API调用 ===');
            
            fetch('/api/recognition/question', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word_id: 1,
                    question_type: 'cn_to_en'
                })
            })
            .then(response => {
                log('API响应状态: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('API响应数据: ' + JSON.stringify(data));
            })
            .catch(error => {
                log('API调用错误: ' + error.message);
            });
        }

        function selectRecognitionOption(option) {
            log('选择选项: ' + option);
            // 简单的选择逻辑
            selectedRecognitionOptions = [option];
            
            // 更新按钮样式
            document.querySelectorAll('.option-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-option="${option}"]`).classList.add('selected');
        }

        function confirmRecognitionAnswer() {
            log('确认答案: ' + selectedRecognitionOptions.join(', '));
        }

        // 复制主要的显示函数
        function displayRecognitionQuestion() {
            log('🚀 开始显示Recognition题目');
            
            if (!currentRecognitionData) {
                log('❌ currentRecognitionData为空');
                return;
            }
            
            log('📋 当前Recognition数据: ' + JSON.stringify(currentRecognitionData));
            
            const { question, question_type, options, correct_answers, is_multiple_choice, total_meanings } = currentRecognitionData;
            
            // 验证必要数据
            if (!options || !Array.isArray(options) || options.length === 0) {
                log('❌ 选项数据无效: ' + JSON.stringify(options));
                return;
            }
            
            log('📝 选项数据: ' + JSON.stringify(options));
            log('✅ 正确答案: ' + JSON.stringify(correct_answers));
            
            // 设置问题文本
            let questionText = question_type === 'cn_to_en' 
                ? `请选择"${question}"的英文：`
                : `请选择"${question}"的中文意思：`;
            
            const questionElement = document.getElementById('question-text');
            if (questionElement) {
                questionElement.textContent = questionText;
                log('✅ 设置问题文本: ' + questionText);
            } else {
                log('❌ 找不到问题文本元素: question-text');
            }
            
            // 设置选项
            log('🔍 开始设置选项，总数: ' + options.length);
            
            options.forEach((option, index) => {
                const optionKey = String.fromCharCode(65 + index); // A, B, C, D
                const optionElement = document.getElementById(`option-${optionKey}-text`);
                const optionButton = document.querySelector(`[data-option="${optionKey}"]`);
                
                log(`🔍 设置选项 ${optionKey}: ${option}`);
                
                if (optionElement) {
                    optionElement.textContent = option;
                    log(`✅ 选项 ${optionKey} 文本已设置: ${option}`);
                } else {
                    log(`❌ 找不到选项元素: option-${optionKey}-text`);
                }
                
                if (optionButton) {
                    optionButton.style.display = 'flex';
                    optionButton.style.visibility = 'visible';
                    optionButton.style.opacity = '1';
                    log(`✅ 选项按钮 ${optionKey} 已显示`);
                } else {
                    log(`❌ 找不到选项按钮: [data-option="${optionKey}"]`);
                }
            });
            
            log('✅ Recognition题目显示完成');
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动检查...');
            testDOMElements();
        });
    </script>
</body>
</html>
