#!/usr/bin/env python3
"""
修复proficiency格式脚本
将数据库中所有proficiency值统一格式化为4位小数
"""

import sqlite3
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)


def fix_proficiency_format():
    """修复所有user_word记录的proficiency格式为4位小数"""
    
    db_path = os.path.join(project_root, 'instance', 'words.db')
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在 {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有需要格式化的user_word记录
        cursor.execute('''
            SELECT id, user_id, word_id, proficiency
            FROM user_word
            WHERE proficiency IS NOT NULL
            ORDER BY user_id, word_id
        ''')
        
        user_words = cursor.fetchall()
        total_records = len(user_words)
        updated_count = 0
        
        print(f"开始修复 {total_records} 条user_word记录的proficiency格式...")
        print("-" * 80)
        
        for record in user_words:
            uw_id, user_id, word_id, old_proficiency = record
            
            # 格式化为4位小数
            new_proficiency = round(float(old_proficiency), 4)
            
            # 只有当格式化后的值与原值不同时才更新
            if new_proficiency != old_proficiency:
                cursor.execute('''
                    UPDATE user_word 
                    SET proficiency = ? 
                    WHERE id = ?
                ''', (new_proficiency, uw_id))
                
                print(f"{updated_count+1:3d}. user_id={user_id:2d}, word_id={word_id:3d} | "
                      f"{old_proficiency} → {new_proficiency}")
                updated_count += 1
        
        # 提交事务
        conn.commit()
        
        print("-" * 80)
        print(f"✅ 成功修复 {updated_count} 条记录的proficiency格式")
        
        # 验证修复结果
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN proficiency != ROUND(proficiency, 4) THEN 1 END) as inconsistent
            FROM user_word 
            WHERE proficiency IS NOT NULL
        ''')
        
        result = cursor.fetchone()
        total, inconsistent = result
        
        print(f"📊 验证结果:")
        print(f"   - 总记录数: {total}")
        print(f"   - 格式不一致的记录: {inconsistent}")
        
        if inconsistent == 0:
            print("✅ 所有proficiency值现在都是4位小数格式")
        else:
            print("⚠️  仍有格式不一致的记录，可能需要进一步检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()


if __name__ == "__main__":
    print("🔧 Proficiency格式修复工具")
    print("=" * 50)
    
    success = fix_proficiency_format()
    
    if success:
        print("\n🎉 修复完成！")
        print("建议运行以下命令验证结果:")
        print("sqlite3 instance/words.db \"SELECT proficiency FROM user_word WHERE proficiency != ROUND(proficiency, 4) LIMIT 5;\"")
    else:
        print("\n❌ 修复失败，请检查错误信息")
        sys.exit(1)
